server:
  port: 8090
spring:
  servlet:
    multipart:
      # 单个文件最大大小
      max-file-size: 10MB
      # 总上传大小限制
      max-request-size: 100MB
      # 文件写入磁盘的阈值
      file-size-threshold: 2KB
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3306/eat?useUnicode=true&characterEncoding=utf8&serverTimezone=GMT%2B8&allowPublicKeyRetrieval=true
    username: root
    password: Tj%&2*hq^aAvUrc!
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 配置初始化大小、最小、最大
      initial-size: 5
      minIdle: 10
      max-active: 20
      # 配置获取连接等待超时的时间(单位：毫秒)
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 2000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 600000
      max-evictable-idle-time-millis: 900000
      # 用来测试连接是否可用的SQL语句,默认值每种数据库都不相同,这是mysql
      validationQuery: select 1
      # 应用向连接池申请连接，并且testOnBorrow为false时，连接池将会判断连接是否处于空闲状态，如果是，则验证这条连接是否可用
      testWhileIdle: true
      # 如果为true，默认是false，应用向连接池申请连接时，连接池会判断这条连接是否是可用的
      testOnBorrow: false
      # 如果为true（默认false），当应用使用完连接，连接池回收连接的时候会判断该连接是否还可用
      testOnReturn: false
      # 是否缓存preparedStatement，也就是PSCache。PSCache对支持游标的数据库性能提升巨大，比如说oracle
      poolPreparedStatements: true
      # 要启用PSCache，必须配置大于0，当大于0时， poolPreparedStatements自动触发修改为true，
      # 在Druid中，不会存在Oracle下PSCache占用内存过多的问题，
      # 可以把这个数值配置大一些，比如说100
      maxOpenPreparedStatements: 20
      # 连接池中的minIdle数量以内的连接，空闲时间超过minEvictableIdleTimeMillis，则会执行keepAlive操作
      keepAlive: true
      # Spring 监控，利用aop 对指定接口的执行时间，jdbc数进行记录
      aop-patterns: "com.dzzh.big_screen.mapper.*"
      ########### 启用内置过滤器（第一个 stat必须，否则监控不到SQL）##########
      filters: stat,wall,log4j2
      # 自己配置监控统计拦截的filter
      filter:
        # 开启druidDatasource的状态监控
        stat:
          enabled: true
          db-type: postgresql
          # 开启慢sql监控，超过2s 就认为是慢sql，记录到日志中
          log-slow-sql: true
          slow-sql-millis: 2000
        # 日志监控，使用slf4j 进行日志输出
        slf4j:
          enabled: true
          statement-log-error-enabled: true
          statement-create-after-log-enabled: false
          statement-close-after-log-enabled: false
          result-set-open-after-log-enabled: false
          result-set-close-after-log-enabled: false
      ########## 配置WebStatFilter，用于采集web关联监控的数据 ##########
      web-stat-filter:
        enabled: true                   # 启动 StatFilter
        url-pattern: /*                 # 过滤所有url
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 排除一些不必要的url
        session-stat-enable: true       # 开启session统计功能
        session-stat-max-count: 1000    # session的最大个数,默认100
      ########## 配置StatViewServlet（监控页面），用于展示Druid的统计信息 ##########
      stat-view-servlet:
        enabled: true                   # 启用StatViewServlet
        url-pattern: /druid/*           # 访问内置监控页面的路径，内置监控页面的首页是/druid/index.html
        reset-enable: false              # 不允许清空统计数据,重新计算
        login-username: root            # 配置监控页面访问密码
        login-password: 123456
        allow: 127.0.0.1           # 允许访问的地址，如果allow没有配置或者为空，则允许所有访问
        deny:
#  data:
#    redis:
#      host: ************
#      port: 6379
#      database: 0
#      timeout: 10s
#      lettuce:
#        pool:
#          # 连接池最大连接数
#          max-active: 200
#          # 连接池最大阻塞等待时间（使用负值表示没有限制）
#          max-wait: -1ms
#          # 连接池中的最大空闲连接
#          max-idle: 10
#          # 连接池中的最小空闲连接
#          min-idle: 0
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: Token-Key
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true
  is-read-cookie: false
  is-read-body: false
decorator:
  datasource:
    p6spy:
      # 日志格式
      log-format: "\ntime:%(executionTime) || sql:%(sql)\n"
      # 自定义日志类
      logging: custom
      custom-appender-class: com.tutu.api.config.mybatis.StdoutLogger
# 文件上传配置
file:
  upload:
    # 文件上传根路径（可以根据实际情况修改）
    base-path: /Users/<USER>/Downloads/files
#    base-path: /opt/data/files/
    # 文件访问URL前缀
    url-prefix: /files
    # 允许上传的文件类型
    allowed-types:
      - jpg
      - jpeg
      - png
      - gif
      - bmp
      - webp
      - pdf
      - doc
      - docx
      - xls
      - xlsx
      - ppt
      - pptx
      - txt
      - zip
      - rar
      - 7z
      - tar
      - gz
      - mp4
      - avi
      - mov
      - wmv
      - flv
      - mkv
      - mp3
      - wav
      - flac
      - aac
    # 单个文件最大大小（字节）10MB
    max-file-size: 10485760
    # 是否按日期分目录存储
    date-folder: true
    # 是否保留原文件名
    keep-original-name: false
wx:
  miniapp:
    configs:
      - appid: wxd34af2ddd3fc15ea
        secret: 9d0cb24e551b012ac1e8e76f9e8a31cc
