package com.tutu.api.config.web;

import com.tutu.api.config.interceptor.AuthInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
@Configuration
public class AppWebConfig implements WebMvcConfigurer {
    @Resource
    private AuthInterceptor authInterceptor;
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/**")
                // 白名单
                .excludePathPatterns("/ad/auth/login",
                        "/wx/auth/login",
                        "/wx/auth/register",
                        "/ad/auth/register",
                        "/system/file/noAuth/upload", // 免登录上传
                        "/files/**");
        WebMvcConfigurer.super.addInterceptors(registry);
    }
}
