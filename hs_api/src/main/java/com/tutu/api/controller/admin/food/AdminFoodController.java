package com.tutu.api.controller.admin.food;

import com.tutu.common.Response.BaseResponse;
import com.tutu.food.entity.food.Food;
import com.tutu.food.schema.FoodSchema;
import com.tutu.food.service.FoodService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@CrossOrigin(originPatterns = "*",allowCredentials="true",allowedHeaders = "*")
@RestController
@RequestMapping("/admin/food")
public class AdminFoodController {




}
