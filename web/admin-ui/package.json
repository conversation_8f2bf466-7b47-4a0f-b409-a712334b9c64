{"name": "element-plus-vite-starter", "type": "module", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.15.0", "license": "MIT", "homepage": "https://vite-starter.element-plus.org", "repository": {"url": "https://github.com/element-plus/element-plus-vite-starter"}, "scripts": {"dev": "vite --mode dev", "prod": "vite --mode prod", "build:dev": "vite build --mode dev", "build:prod": "vite build --mode prod", "generate": "vite-ssg build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.0.0", "axios": "^1.7.9", "echarts": "^5.6.0", "element-plus": "^2.9.0", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@antfu/eslint-config": "^3.11.2", "@iconify-json/ep": "^1.2.1", "@iconify-json/ri": "^1.2.3", "@types/node": "^20.17.10", "@unocss/eslint-plugin": "^0.65.1", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.16.0", "eslint-plugin-format": "^0.1.3", "sass": "^1.82.0", "typescript": "^5.6.3", "unocss": "^0.65.1", "unplugin-vue-components": "^0.27.5", "unplugin-vue-router": "^0.10.9", "vite": "^6.0.3", "vite-ssg": "^0.24.2", "vue-tsc": "^2.1.10"}}