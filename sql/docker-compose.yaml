version: '3'
services:
  eat:
    image: openjdk:21
    restart: always
    ports:
      - "8081:8081"
    command: "java -jar /opt/data/eat.jar"
    volumes:
      - ./data/eat_api-0.0.1-SNAPSHOT-exec.jar:/opt/data/eat.jar
  db:
    image: mysql:latest
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: Tj%&2*hq^aAvUrc!
    ports:
      - "3306:3306"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/mysql/data:/var/lib/mysql
      - ./data/mysql/log:/var/log/mysql
      - ./data/mysql/conf:/etc/mysql/conf.d
  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
      - ./data/redis/conf/redis.conf:/etc/redis/redis.conf
  nginx:
    image: nginx:latest
    restart: always
    ports:
      - "80:80"
    volumes:
      - ./data/nginx/conf.d:/etc/nginx/conf.d
      - ./data/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./data/nginx/log:/var/log/nginx
      - ./data/nginx/html:/usr/share/nginx/html