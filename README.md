# eat
## 需求分析
1. ~~用户可以存储自己的食物信息~~
2. 用户可以查看自己的就餐记录
有个管理列表可以查询或者删除，无法修改
然后有个日历，可以查看自己的就餐记录，点击可以查看详细信息

3. 绑定家庭成员一起吃
4. 可以看到别人记录的饮食，一个分享社区，位置同步，一个区块内多人分享
5. 可以选择：外卖、堂食、自己做
6. 可以选择：自己做的话，需要知道自己的材料，需要知道自己的制作步骤
7. 可以选择：外卖的话，需要知道自己的地址，需要知道自己的联系方式
8. 可以选择：堂食的话，需要知道自己的位置，需要知道自己的联系方式
9. 食物打标签，方便查找，然后可以对标签进行组合命名成习惯，就可以进行对习惯的随机选择
10. 可以根据家里的食材，推荐适合的食物
11. 家庭共享，方便家庭成员一起吃
12. 投票选择吃什么
11. 如果别人把他的菜单公开，那么可以将他的菜单加入到自己，或者从他的菜单中选择食物
12. 食物中可以配置有哪些食材

食物相关
1. 食物类型
技术相关
1. 工具类：自动的根据id查询对应name名称
2. 前端：学习如何改造日历组件
3. redis 缓存乱码问题
用户相关
1. 角色
2. 权限
3. 注册
4. 菜单权限控制

开发日志
2025-4-1：新增饮食记录
2025-4-3：暂时补充了角色权限，但是任然需要有完善的菜单权限控制
2025-5-13：优化了登录、注册界面

部署流程
1. 先clean，再对common进行package
2. 再对其他模块进行package
3. 编译前端
4. nvm use 20.18.0
5. npm run build:prod