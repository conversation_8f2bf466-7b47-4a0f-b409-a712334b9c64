package com.tutu.admin_user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tutu.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 角色实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdRole extends BaseEntity {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    
    /**
     * 角色名
     */
    private String name;
    
    /**
     * 角色编码
     */
    private String code;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 权限列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<AdPermission> adPermissions;
}
